/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     app_func.c
 *  @brief    Application function implementations for NFC lock firmware
 *
 *  This module contains all app_func_0 through app_func_15 function implementations
 *  used by the mailbox system for various application functionalities.
 */

// standard libs
#include "core_cm0.h"
#include <stdbool.h>

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib
#include "smack_exchange.h"

// Project includes
#include "app_func.h"
#include "challenge.h"
#include "my_exchange_handler.h"

/**
 * @brief Generic application function implementation
 * 
 * This function provides a standard response pattern for generic app functions.
 * It sets up the mailbox with an ID pattern and counter.
 * 
 * @param id The function ID to be written to mailbox
 */
static void app_func_generic(uint32_t id)
{
    Mailbox_t *mbox;
    static uint32_t counter = 0;
    mbox = get_mailbox_address();
    mbox->content[0] = id;
    mbox->content[1] = 0;
    mbox->content[2] = counter++;
    mbox->content[3] = id;
}

void app_func_0(void)
{
    // app_func_generic(0x00000000);

    if (!challenge_is_passed())
    {
        return;
    }

    smack_exchange_handler();
}

void app_func_1(void)
{
    // app_func_generic(0x11111111);

    my_exchange_handler();
}

void app_func_2(void)
{
    app_func_generic(0x22222222);
}

void app_func_3(void)
{
    app_func_generic(0x33333333);
}

void app_func_4(void)
{
    app_func_generic(0x44444444);
}

void app_func_5(void)
{
    app_func_generic(0x55555555);
}

void app_func_6(void)
{
    app_func_generic(0x66666666);
}

void app_func_7(void)
{
    app_func_generic(0x77777777);
}

void app_func_8(void)
{
    app_func_generic(0x88888888);
}

void app_func_9(void)
{
    app_func_generic(0x99999999);
}

void app_func_10(void)
{
    app_func_generic(0xaaaaaaaa);
}

void app_func_11(void)
{
    // app_func_generic(0xbbbbbbbb);

    challenge_validate_response();
}

void app_func_12(void)
{
    // app_func_generic(0xcccccccc);

    challenge_generate_value();
}

void app_func_13(void)
{
    app_func_generic(0xdddddddd);
}

void app_func_14(void)
{
    app_func_generic(0xeeeeeeee);
}

void app_func_15(void)
{
    app_func_generic(0xffffffff);
}
