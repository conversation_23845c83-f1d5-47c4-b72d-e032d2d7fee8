/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     challenge.c
 *  @brief    Challenge value validation module implementation
 *
 *  This module provides challenge-response authentication functionality
 *  for secure access control in the NFC lock firmware.
 */

#include "challenge.h"
#include "settings.h"

// Global variables
uint32_t challenge_value = 0;
bool challenge_passed = false;

bool challenge_is_passed(void)
{
#if CHALLENGE_ENABLE
  if (challenge_passed)
  {
    challenge_passed = false;
    return true;
  }
  else
  {
    Mailbox_t *p;
    p = get_mailbox_address();
    int t;
    for (t = 0; t <= 15; t++)
    {
      p->content[t] = 0x0;
    }
    return false;
  }
#else
  // Challenge is disabled, always return true
  return true;
#endif
}

void challenge_validate_response(void)
{
  Mailbox_t *p;
  p = get_mailbox_address();
  if (p->content[1] == challenge_value)
  {
    challenge_passed = true;
    p->content[0] = 0x1;
  }
  else
  {
    challenge_passed = false;
    p->content[0] = 0x0;
  }
}

void challenge_generate_value(void)
{
  Mailbox_t *p;
  p = get_mailbox_address();
  p->content[1] = p->content[1] ^ 0x12345678;
  p->content[0] = get_random_u32(p->content[1]);
  p->content[2] = get_random_u32(p->content[0]);
  p->content[3] = get_random_u32(p->content[2]);
  uint32_t c0 = (p->content[0] >> 24) * (p->content[1] >> 16) * (p->content[2] >> 8) * (p->content[3]);

  p->content[4] = get_random_u32(p->content[3]);
  p->content[5] = get_random_u32(p->content[4]);
  p->content[6] = get_random_u32(p->content[5]);
  p->content[7] = get_random_u32(p->content[6]);
  uint32_t c1 = (p->content[4] >> 24) + (p->content[5] >> 16) + (p->content[6] >> 8) + (p->content[7]);

  p->content[8] = get_random_u32(p->content[7]);
  p->content[9] = get_random_u32(p->content[8]);
  p->content[10] = get_random_u32(p->content[9]);
  p->content[11] = get_random_u32(p->content[10]);
  uint32_t c2 = (p->content[8] >> 24) | (p->content[9] >> 16) | (p->content[10] >> 8) | (p->content[11]);

  p->content[12] = get_random_u32(p->content[11]);
  p->content[13] = get_random_u32(p->content[12]);
  p->content[14] = get_random_u32(p->content[13]);
  p->content[15] = get_random_u32(p->content[14]);
  uint32_t c3 = (p->content[12] >> 24) & (p->content[13] >> 16) & (p->content[14] >> 8) & (p->content[15]);

  challenge_value = 0x2bd16c3d - (c0 ^ c1 ^ c2 ^ c3);
}
