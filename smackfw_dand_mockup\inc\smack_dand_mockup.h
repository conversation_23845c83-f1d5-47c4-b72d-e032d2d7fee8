/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/**
 * @file     smack_dand_mockup.h
 *
 * @brief    Smack dandelion test example file.
 *
 * @version  v1.0
 * @date     2020-05-20
 *
 * @note
 */

/* lint -save -e960 */

#ifndef _SMACK_DAND_MOCKUP_H_
#define _SMACK_DAND_MOCKUP_H_


/** @addtogroup Infineon
 * @{
 */

/** @addtogroup Smack_dand_mockup
 * @{
 */


/** @addtogroup fw_config
 * @{
 */


/* Debug support: define DEBUG to enable, undef to disable.
 * enabling debug support requires additional modules/source files not present in the Git repo.
 * If debugging is desired, check for printf.* and uart_lib.* files in the smack_test_v3 branch,
 * and also check for modifications to the project Makefile there.
 */
//#define DEBUG   1

#if defined DEBUG && DEBUG
#include "debug_mockup.h"
#else

#define debug_init()
#define debug_task()
#define debug_log_de(a,b,c)
#define dbgp(...)

#endif

// mbox debug:

extern void dump_config(void);
extern void dump_config_bcd(void);
extern void nvm_erase_content(void);

extern void hard_fault_delay(void);


extern void smack_exchange_handler_wrapper(void);

// notification functions, one per access, used to control state machines
extern void data_rx(uint16_t datapoint);
extern void data_tx(uint16_t datapoint);


/** @} */ /* End of group fw_config */


/** @} */ /* End of group Smack_dand_mockup */

/** @} */ /* End of group Infineon */

#endif /* _SMACK_DAND_MOCKUP_H_ */
