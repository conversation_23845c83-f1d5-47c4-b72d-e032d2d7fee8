#include "sensor_util.h"

void get_sensor_value(int16_t *x, int16_t *y, int16_t *z, uint16_t *t)
{
  sensor_init_sf();
  sensor_read_sf(x, y, z, t);
}

bool is_target_reached()
{
  int16_t x, y, z;
  uint16_t t;
  get_sensor_value(&x, &y, &z, &t);

  my_config_load();
  int16_t x_from, x_to, y_from, y_to, z_from, z_to;
  x_from = my_config.value1 >> 16;
  x_to = my_config.value1 & 0xffff;
  y_from = my_config.value2 >> 16;
  y_to = my_config.value2 & 0xffff;
  z_from = my_config.value3 >> 16;
  z_to = my_config.value3 & 0xffff;

  if (x_from < 0 || x_to < 0 || y_from < 0 || y_to < 0 || z_from < 0 || z_to < 0)
  {
    return true;
  }
  else
  {
    return (
        abs(x) >= x_from &&
        abs(x) <= x_to &&
        abs(y) >= y_from &&
        abs(y) <= y_to &&
        abs(z) >= z_from &&
        abs(z) <= z_to);
  }
}
