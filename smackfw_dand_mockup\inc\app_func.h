/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     app_func.h
 *  @brief    Application function declarations for NFC lock firmware
 *
 *  This module contains all app_func_0 through app_func_15 function declarations
 *  used by the mailbox system for various application functionalities.
 */

#ifndef _APP_FUNC_H_
#define _APP_FUNC_H_

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Application function 0
 * 
 * Handles smack exchange functionality.
 */
extern void app_func_0(void);

/**
 * @brief Application function 1
 * 
 * Handles custom data exchange functionality via my_exchange_handler.
 */
extern void app_func_1(void);

/**
 * @brief Application function 2
 * 
 * Generic application function.
 */
extern void app_func_2(void);

/**
 * @brief Application function 3
 * 
 * Generic application function.
 */
extern void app_func_3(void);

/**
 * @brief Application function 4
 * 
 * Generic application function.
 */
extern void app_func_4(void);

/**
 * @brief Application function 5
 * 
 * Generic application function.
 */
extern void app_func_5(void);

/**
 * @brief Application function 6
 * 
 * Generic application function.
 */
extern void app_func_6(void);

/**
 * @brief Application function 7
 * 
 * Generic application function.
 */
extern void app_func_7(void);

/**
 * @brief Application function 8
 * 
 * Generic application function.
 */
extern void app_func_8(void);

/**
 * @brief Application function 9
 * 
 * Generic application function.
 */
extern void app_func_9(void);

/**
 * @brief Application function 10
 * 
 * Generic application function.
 */
extern void app_func_10(void);

/**
 * @brief Application function 11
 * 
 * Handles challenge response validation.
 */
extern void app_func_11(void);

/**
 * @brief Application function 12
 * 
 * Handles challenge value generation.
 */
extern void app_func_12(void);

/**
 * @brief Application function 13
 * 
 * Generic application function.
 */
extern void app_func_13(void);

/**
 * @brief Application function 14
 * 
 * Generic application function.
 */
extern void app_func_14(void);

/**
 * @brief Application function 15
 * 
 * Generic application function.
 */
extern void app_func_15(void);

#ifdef __cplusplus
}
#endif

#endif /* _APP_FUNC_H_ */
