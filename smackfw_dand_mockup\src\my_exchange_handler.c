/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     my_exchange_handler.c
 *  @brief    Custom data exchange handler implementation for NFC lock firmware
 *
 *  This module provides a centralized handler for various data exchange functions
 *  based on mailbox content[1] values.
 */

// standard libs
#include "core_cm0.h"
#include <stdbool.h>
#include <string.h>

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib
#include "system_lib.h"

// Project includes
#include "my_exchange_handler.h"
#include "challenge.h"
#include "my_nvm.h"
#include "smack_dand_data.h"
#include "sensor_util.h"

void my_exchange_handler(void)
{
    // First check if challenge is passed
    if (!challenge_is_passed())
    {
        return;
    }

    Mailbox_t *p = get_mailbox_address();
    uint8_t function_code = (uint8_t)(p->content[1] & 0xFF);

    switch (function_code)
    {
    case MY_EXCHANGE_FUNC_CONFIG_MODIFY:
        handle_config_modify();
        break;

    case MY_EXCHANGE_FUNC_USER_KEY_ERASE:
        handle_user_key_erase();
        break;

    case MY_EXCHANGE_FUNC_RF_STATUS:
        handle_rf_status();
        break;

    case MY_EXCHANGE_FUNC_SENSOR_READ:
        handle_sensor_read();
        break;

    default:
        // Unknown function code, clear mailbox
        p = get_mailbox_address();
        for (int i = 0; i < 16; i++)
        {
            p->content[i] = 0x0;
        }
        break;
    }
}

void handle_config_modify(void)
{
    Mailbox_t *p = get_mailbox_address();

    // Load current config, modify values, and save
    my_config_load();
    my_config.value1 = p->content[2];
    my_config.value2 = p->content[3];
    my_config.value3 = p->content[4];
    my_config.value4 = p->content[5];
    my_config.value5 = p->content[6];
    my_config.value6 = p->content[7];
    my_config.value7 = p->content[8];
    my_config.value8 = p->content[9];
    my_config_save();
}

void handle_user_key_erase(void)
{
    Mailbox_t *p = get_mailbox_address();

    // Compare the provided SU key (from mailbox content[2-5]) with stored SU key
    // The SU key is 16 bytes, stored in content[2-5] (4 uint32_t values)
    bool su_key_match = true;
    const uint32_t *stored_su_key = (const uint32_t *)config_device.su_key.b;

    for (int i = 0; i < 4; i++)
    {
        if (p->content[2 + i] != stored_su_key[i])
        {
            su_key_match = false;
            break;
        }
    }

    if (su_key_match)
    {
        // SU key matches, erase user key
        session.erase |= 0x01;
        p->content[2] = 0x1;  // Success
    }
    else
    {
        // SU key doesn't match
        p->content[2] = 0x0;  // Failure
    }
}

void handle_rf_status(void)
{
    Mailbox_t *p = get_mailbox_address();
    bool field_present = check_rf_field();

    if (field_present)
    {
        uint32_t rssi_raw = message_get_rssi(p);
        uint32_t vccca_raw = message_get_vccca(p);
        p->content[2] = rssi_raw;
        p->content[3] = vccca_raw;
    }
    else
    {
        p->content[2] = 0;
        p->content[3] = 0;
    }
}

void handle_sensor_read(void)
{
    Mailbox_t *p = get_mailbox_address();

    int16_t x, y, z;
    uint16_t t;
    get_sensor_value(&x, &y, &z, &t);
    bool reached = is_target_reached();

    p->content[2] = x;
    p->content[3] = y;
    p->content[4] = z;
    p->content[5] = t;
    p->content[6] = reached;
}
