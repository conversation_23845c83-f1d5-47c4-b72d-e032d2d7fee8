# 宏重定义修复说明

## 问题描述

编译时出现宏重定义警告：

```
In file included from E:/current_project/NFC锁固件/smackfw_dand_mockup/src/motor_one_shot.c:41:   
E:/current_project/NFC锁固件/smackfw_dand_mockup/inc/motor_common.h:47: warning: "ms2ticks" redefined
   47 | #define ms2ticks(ms)    ((ms) * (wait_about_1ms))
      |
In file included from E:/current_project/NFC锁固件/smackfw_dand_mockup/inc/util.h:15,
                 from E:/current_project/NFC锁固件/smackfw_dand_mockup/src/motor_one_shot.c:23:   
E:/current_project/NFC锁固件/smack_lib/inc/sys_tick_lib.h:45: note: this is the location of the previous definition
   45 | #define ms2ticks(ms)    (ms * (XTAL / 1000UL))
      |
```

## 原因分析

`ms2ticks` 宏在两个地方定义了：

1. **motor_common.h:47**:
   ```c
   #define ms2ticks(ms)    ((ms) * (wait_about_1ms))
   ```

2. **sys_tick_lib.h:45**:
   ```c
   #ifndef ms2ticks
   #define ms2ticks(ms)    (ms * (XTAL / 1000UL))
   #endif
   ```

由于 `motor_common.h` 在 `sys_tick_lib.h` 之后被包含，导致重定义警告。

## 解决方案

### 修改内容

在 `inc/motor_common.h` 中进行了以下修改：

1. **添加了 sys_tick_lib.h 的引用**：
   ```c
   // Include sys_tick_lib for ms2ticks macro
   #include "sys_tick_lib.h"
   ```

2. **注释掉了重复的宏定义**：
   ```c
   // Use ms2ticks from sys_tick_lib.h instead of redefining it here
   // #define ms2ticks(ms)    ((ms) * (wait_about_1ms))
   ```

3. **保留了 wait_about_1ms 定义**：
   ```c
   #undef wait_about_1ms
   #define wait_about_1ms  (XTAL / 1000U)          // calculated from oscillator; more exact than rough estimate
   ```

### 设计考虑

1. **统一使用 sys_tick_lib.h 的定义**：
   - `sys_tick_lib.h` 是系统级别的时间库
   - 使用 `#ifndef` 保护，避免重复定义
   - 定义更加标准化：`(ms * (XTAL / 1000UL))`

2. **保持向后兼容**：
   - 在 `motor_common.h` 中引入 `sys_tick_lib.h`
   - 使用者包含 `motor_common.h` 时自动获得 `ms2ticks` 宏
   - 不需要修改使用 `motor_common.h` 的代码

3. **保留 wait_about_1ms**：
   - 可能在其他地方被使用
   - 提供了更精确的时间计算

## 验证结果

### 编译测试
✅ **警告消除**: 宏重定义警告不再出现
✅ **编译成功**: 代码正常编译，没有错误
✅ **功能保持**: 所有使用 `ms2ticks` 的代码继续正常工作

### 宏定义统一
现在所有地方使用的 `ms2ticks` 都来自 `sys_tick_lib.h`：
```c
#define ms2ticks(ms)    (ms * (XTAL / 1000UL))
```

## 影响范围

### 直接影响的文件
- `inc/motor_common.h` - 修改了宏定义
- `src/motor_one_shot.c` - 不再有重定义警告
- 其他包含 `motor_common.h` 的文件 - 自动使用统一的宏定义

### 间接影响
- 所有使用 `ms2ticks` 的代码现在使用统一的定义
- 时间计算更加一致和准确

## 最佳实践

1. **避免重复定义宏**：
   - 使用系统库提供的标准宏
   - 在自定义头文件中引用系统库

2. **使用 #ifndef 保护**：
   ```c
   #ifndef MACRO_NAME
   #define MACRO_NAME value
   #endif
   ```

3. **统一时间相关宏**：
   - 优先使用 `sys_tick_lib.h` 中的时间宏
   - 保持时间计算的一致性

## 后续建议

1. **检查其他重复宏**：
   - 搜索项目中是否还有其他重复定义的宏
   - 统一使用系统库提供的标准定义

2. **文档更新**：
   - 更新相关文档，说明使用 `sys_tick_lib.h` 的 `ms2ticks`
   - 在代码注释中说明宏的来源

3. **代码审查**：
   - 在代码审查中检查宏定义的重复
   - 确保新代码使用标准库的宏定义
