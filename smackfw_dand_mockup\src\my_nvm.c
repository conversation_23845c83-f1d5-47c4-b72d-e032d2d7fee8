/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     my_nvm.c
 *  @brief    Custom configuration storage for NFC lock firmware
 */

// standard libs
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

// Smack ROM lib
#include "rom_lib.h"

// Custom NVM implementation
#include "my_nvm.h"
#include "my_nvm_data.h"

//---------------------------------------------------------
// persistent my configuration

// Global configuration instance in RAM
my_config_t my_config;

static uint8_t my_config_index = 0xff;
static uint8_t my_config_seq;

static uint8_t my_config_get_index(void)
{
    int16_t idx = -1;
    int16_t idx2;
    int i;

    // Find the most recent valid configuration entry
    for (i = 0; i < MY_CONFIG_NVM_COUNT; i++)
    {
        if (my_config_nvm[i].magic == MY_CONFIG_MAGIC)
        {
            idx2 = i - 1;

            if (idx2 < 0)
            {
                idx2 = MY_CONFIG_NVM_COUNT - 1;
            }

            if (my_config_nvm[idx2].magic != MY_CONFIG_MAGIC ||
                    (my_config_nvm[i].seq == (uint8_t)(my_config_nvm[idx2].seq + 1)))
            {
                idx = i;
            }
        }
    }

    if (idx >= 0)
    {
        my_config_index = idx;
        my_config_seq = my_config_nvm[idx].seq;
    }
    else
    {
        my_config_index = 0;
        my_config_seq = 0;
    }

    return my_config_index;
}

bool my_config_load(void)
{
    uint8_t index;
    bool rc = false;

    // Get the index of the most recent valid configuration
    index = my_config_get_index();

    if (my_config_nvm[index].magic == MY_CONFIG_MAGIC)
    {
        my_config = my_config_nvm[index].config;
        rc = true;
    }
    else
    {
        my_config_set_default();
    }

    return rc;
}

void my_config_save(void)
{
    uint8_t index;
    my_config_nvm_t* p;
    static uint32_t rc __attribute__((unused));

    index = my_config_get_index();

    // Only save if configuration has changed
    if (memcmp(&my_config_nvm[index].config, &my_config, sizeof(my_config)))
    {
        index++;

        if (index >= MY_CONFIG_NVM_COUNT)
        {
            index = 0;
        }

        p = (my_config_nvm_t*)&my_config_nvm[index];
        rc = nvm_open_assembly_buffer((uint32_t)p);
        p->config = my_config;
        p->seq = my_config_seq + 1;
        p->magic = MY_CONFIG_MAGIC;
        nvm_program_page();
        nvm_config();
        my_config_seq = p->seq;
        my_config_index = index;
    }
}

void my_config_set_default(void)
{
    // Set default values for all configuration parameters
    const my_config_t cfg_default =
    {
        .value1 = 0xFFFFFFFF,
        .value2 = 0xFFFFFFFF,
        .value3 = 0xFFFFFFFF,
        .value4 = 0xFFFFFFFF,
        .value5 = 0xFFFFFFFF,
        .value6 = 0xFFFFFFFF,
        .value7 = 0xFFFFFFFF,
        .value8 = 0xFFFFFFFF,
    };
    my_config = cfg_default;
}

void my_config_clear(void)
{
    uint16_t index;

    // Clear all configuration entries from NVM
    for (index = 0; index < MY_CONFIG_NVM_COUNT; index++)
    {
        if (my_config_nvm[index].magic == MY_CONFIG_MAGIC)
        {
            nvm_open_assembly_buffer((uint32_t)&my_config_nvm[index]);
            nvm_erase_page();
            nvm_config();
        }
    }

    // Reset to default configuration
    my_config_set_default();
}