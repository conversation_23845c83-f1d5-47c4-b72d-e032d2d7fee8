/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     my_nvm_data.h
 *  @brief    Custom configuration storage data structures for NFC lock firmware
 */

/* lint -save -e960 */

#ifndef _MY_NVM_DATA_H_
#define _MY_NVM_DATA_H_

#include <stdint.h>
#include "my_nvm.h"

// Include NVM library for constants
#include "rom_lib.h"

/** @addtogroup Infineon
 * @{
 */

/** @addtogroup My_nvm_data
 * @{
 */

/** @addtogroup my_config_data
 * @{
 */

#ifndef NVM_PAGE_SIZE   /* This macro may be defined in nvm.h in the future */
#define NVM_PAGE_SIZE (NVM_SIZE / NUMBER_OF_SECTORS / N_LOG_PAGES)    // should give page size of 128 bytes
#endif

// storage for persistent part of my configuration in NVM
typedef union
{
    uint8_t b[NVM_PAGE_SIZE];
    struct
    {
        my_config_t config;
        uint8_t magic;
        uint8_t seq;
    };
} my_config_nvm_t;

#define MY_CONFIG_MAGIC         (0x54)
#define MY_CONFIG_NVM_COUNT     2

extern const my_config_nvm_t my_config_nvm[MY_CONFIG_NVM_COUNT];

/** @} */ /* End of group my_config_data */

/** @} */ /* End of group My_nvm_data */

/** @} */ /* End of group Infineon */

#endif /* _MY_NVM_DATA_H_ */