/******************************************************************************
 * @file     gcc_arm.ld
 * @brief    GNU Linker Script for Cortex-M based device
 * @version  V2.0.0
 * @date     21. May 2019
 ******************************************************************************/
/*
 * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

OUTPUT_FORMAT("elf32-littlearm")
OUTPUT_ARCH(arm)


INCLUDE smack_memory.ld

section_version_base = __NVM_BASE + __NVM_SIZE;
MEMORY
{
	/* holds version and code identification for NVM application firmware*/
	version (r) : ORIGIN = section_version_base, LENGTH = section_version_size
}


/* smackfw_dand_mockup:
 *
 * Define memory blocks from the back to the front, e.g. start with data storage of known size and
 * data which shall survive firmware updates at the end, then place dynamic data such as logs in
 * front of them, and use the remaining space at the beginning of the user's NVM area for the text
 * segment.
 *
 * All sections that sourced into the NVM memory block in the standard smack_sl example are modified
 * to source into NVM_TEXT now. There shall be no use of the NVM memory block in this script!
 *
 * Assume NVM sector size of 128 bytes for all calculations.
 */


/*
memory layout:
Group sections according to write frequency and place them into different sectors to 
reduce side effects of write operations to cells in other pages. Define separate base
labels of each sector of 4k.

trimming/parametrization/firmware:
- DPARAM
- APARAM
- firmware
dynamic data:
- history (log)
- user key
- user configuration
static data:
- NDEF tag
- supervisor key
- lock ID
- Smack version struct
 */

/* Last sector is used for static storage: lock UID/supervisor key, NDEF tag */
/* We calculate addresses from the end. __NVM_SIZE already excludes section_version_base */
__NVM_SECTORS_STATIC_END = (__NVM_BASE + __NVM_SIZE) & 0xffffff80;          /* end at boundary to last page */
__NVM_SECTORS_STATIC_BASE = (__NVM_SECTORS_STATIC_END - 1) & 0xfffff000;    /* set to start of sector */
__NVM_SECTORS_STATIC_SIZE = __NVM_SECTORS_STATIC_END - __NVM_SECTORS_STATIC_BASE;

/* storage for NDEF type 2 tag */
/* 128 bytes may be a bit short in some cases, so we reserve two pages */
__NVM_NDEF_SIZE = 0x0100;
__NVM_NDEF_BASE = __NVM_SECTORS_STATIC_BASE;

/* storage for constant device data (customized UID etc.) */
/* 1 page (128 bytes) for only small static data */
__NVM_CONSTANT_SIZE = 0x0080;
__NVM_CONSTANT_BASE = __NVM_SECTORS_STATIC_BASE + 0x0400;


/* one sector for rarely modified dynamic data */
__NVM_SECTORS_DYNAMIC_LOW_SIZE = 0x1000;
__NVM_SECTORS_DYNAMIC_LOW_BASE = __NVM_SECTORS_STATIC_BASE - __NVM_SECTORS_DYNAMIC_LOW_SIZE;

/* storage for user configuration */
/* 128 bytes max. for one config set (e.g. user ID, password), 16 entries -> 16 pages (1/2 sector) */
__NVM_USER_SIZE = 0x0080 * 16;
__NVM_USER_BASE = __NVM_SECTORS_DYNAMIC_LOW_BASE;

/* storage for persistent configuration */
/* 128 bytes max. for one config set (e.g. motor on time) organized as 2-entry flip buffer -> two pages */
__NVM_CONFIG_SIZE = 0x0080 * 2;
__NVM_CONFIG_BASE = __NVM_USER_BASE + __NVM_USER_SIZE;

/* storage for my custom configuration */
/* 128 bytes max. for one config set organized as 2-entry flip buffer -> two pages */
__NVM_MY_CONFIG_SIZE = 0x0080 * 2;
__NVM_MY_CONFIG_BASE = __NVM_CONFIG_BASE + __NVM_CONFIG_SIZE;


/* one sector for more often modified dynamic data */
__NVM_SECTORS_DYNAMIC_HIGH_SIZE = 0x1000;
__NVM_SECTORS_DYNAMIC_HIGH_BASE = __NVM_SECTORS_DYNAMIC_LOW_BASE - __NVM_SECTORS_DYNAMIC_HIGH_SIZE;

/* storage for log entries */
/* assume 128 bytes max. for one log entry, 16 entries -> 16 pages */
/* firmware may organize logs as entries of 64 bytes each, and then will be able to store 32 entries */
__NVM_LOG_SIZE = 0x0080 * 16;
__NVM_LOG_BASE = __NVM_SECTORS_DYNAMIC_HIGH_BASE;




/* padding of unused area in last page, just before version struct */
__NVM_PADDING_BASE = (__NVM_BASE + __NVM_SIZE) & 0xffffff80;
__NVM_PADDING_SIZE = __NVM_BASE + __NVM_SIZE - __NVM_PADDING_BASE;

/* memory region for program code */
/* use remaining NVM after DPARAM up to log area */
__NVM_TEXT_BASE = __NVM_BASE;
__NVM_TEXT_SIZE = __NVM_LOG_BASE - __NVM_TEXT_BASE;

MEMORY
{
	NVM_TEXT (rx)    : ORIGIN = __NVM_TEXT_BASE,      LENGTH = __NVM_TEXT_SIZE
	NVM_DYNAMIC_HIGH (rx) : ORIGIN = __NVM_SECTORS_DYNAMIC_HIGH_BASE, LENGTH = __NVM_SECTORS_DYNAMIC_HIGH_SIZE
	NVM_DYNAMIC_LOW (rx)  : ORIGIN = __NVM_SECTORS_DYNAMIC_LOW_BASE,  LENGTH = __NVM_SECTORS_DYNAMIC_LOW_SIZE
	NVM_STATIC (rx)       : ORIGIN = __NVM_SECTORS_STATIC_BASE,       LENGTH = __NVM_SECTORS_STATIC_SIZE
/*
	NVM_LOG (rx)     : ORIGIN = __NVM_LOG_BASE,       LENGTH = __NVM_LOG_SIZE
	NVM_USER (rx)    : ORIGIN = __NVM_USER_BASE,      LENGTH = __NVM_USER_SIZE
	NVM_CONFIG (rx)  : ORIGIN = __NVM_CONFIG_BASE,    LENGTH = __NVM_CONFIG_SIZE
	NVM_NDEF (rx)    : ORIGIN = __NVM_NDEF_BASE,      LENGTH = __NVM_NDEF_SIZE
	NVM_CONSTANT (rx): ORIGIN = __NVM_CONSTANT_BASE,  LENGTH = __NVM_CONSTANT_SIZE
*/
	NVM_PADDING (rx) : ORIGIN = __NVM_PADDING_BASE,   LENGTH = __NVM_PADDING_SIZE
}

/* Linker script to place sections and symbol values. Should be used together
 * with other linker script that defines memory regions ROM and RAM.
 * It references following symbols, which must be defined in code:
 *   Reset_Handler : Entry of reset handler
 *
 * It defines following symbols, which code can use without definition:
 *   __exidx_start
 *   __exidx_end
 *   __nvm_copy_table_start__
 *   __nvm_copy_table_end__
 *   __nvm_zero_table_start__
 *   __nvm_zero_table_end__
 *   __etext
 *   __data_start__
 *   __preinit_array_start
 *   __preinit_array_end
 *   __init_array_start
 *   __init_array_end
 *   __fini_array_start
 *   __fini_array_end
 *   __data_end__
 *   __bss_start__
 *   __bss_end__
 *   __end__
 *   end
 *   __HeapLimit
 *   __StackLimit
 *   __StackTop
 *   __stack
 */
ENTRY(NVM_Reset_Handler)


SECTIONS
{
	/* ------------------------------------------------------------------------ */

	/* Create a section that holds the FW version and code identification.
	 */
	.version :
	{
		/* make sure linker is not throwing it away, post-build step needs it */
		/* @todo name changes when file name changes ... nice.*/
		KEEP(*(.rodata.version))
	} > version

    /* The SMACK Romcode reserves some space in the NVM for device and application parameters
       named DPARAMs and APARAMs
       The DPARAMs will be filled by ATE testing and the APARAMs will be filled by NVM flashing
       of Smack application code by customers
       NVM pages  0-10 are reserved for the DPARAM data structure,
       NVM pages 11-13 are reserved for secret APARAMs, and
       NVM pages 14-15 are reserved for other APARAMs
       Note: this leaves 464 pages for NVM firmware 
     */

	.dparam (NOLOAD):
	{
		. = ORIGIN(DPARAM);
		__nvm_dparam_section_start__ = .;
		/* ../smack_rom/build/dparams/objects/default_dparam.o(.nvm.DPARAMS); */
		KEEP(*(.nvm.DPARAMS))
		__nvm_dparam_section_end__ = .;
	} > DPARAM

	.aparam :
	{
		__nvm_aparam_section_start__ = .;
		KEEP(*(.nvm.APARAMS))
		__nvm_aparam_section_end__ = .;
	} > APARAM = 0xffff

	.text :
	{
		__NVM_FIRMWARE_START = .;
		
		KEEP(*(.vectors))
		*(.text*)

		KEEP(*(.init))
		KEEP(*(.fini))

		/* .ctors */
		*crtbegin.o(.ctors)
		*crtbegin?.o(.ctors)
		*(EXCLUDE_FILE(*crtend?.o *crtend.o) .ctors)
		*(SORT(.ctors.*))
		*(.ctors)

		/* .dtors */
		*crtbegin.o(.dtors)
		*crtbegin?.o(.dtors)
		*(EXCLUDE_FILE(*crtend?.o *crtend.o) .dtors)
		*(SORT(.dtors.*))
		*(.dtors)

		*(.rodata*)

		KEEP(*(.eh_frame*))
	} > NVM_TEXT = 0xffff

	/* SG veneers:
	   All SG veneers are placed in the special output section .gnu.sgstubs. Its start address
	   must be set, either with the command line option ‘--section-start’ or in a linker script,
	   to indicate where to place these veneers in memory.
	 */
	.gnu.sgstubs :
	{
		. = ALIGN(32);
	} > NVM_TEXT = 0xffff

	.ARM.extab :
	{
		*(.ARM.extab* .gnu.linkonce.armextab.*)
	} > NVM_TEXT = 0xffff

	__exidx_start = .;
	.ARM.exidx :
	{
		*(.ARM.exidx* .gnu.linkonce.armexidx.*)
	} > NVM_TEXT = 0xffff
	__exidx_end = .;

	.copy.table :
	{
		. = ALIGN(4);
		__nvm_copy_table_start__ = .;
		LONG (__etext)
		LONG (__data_start__)
		LONG ((__data_end__ - __data_start__) / 4)
    /** Add each additional data section here */
    /*
		LONG (__etext2)
		LONG (__data2_start__)
		LONG ((__data2_end__ - __data2_start__) / 4)
	*/
		__nvm_copy_table_end__ = .;
	} > NVM_TEXT = 0

	.zero.table :
	{
		. = ALIGN(4);
		__nvm_zero_table_start__ = .;
		LONG (__bss_start__)
		LONG ((__bss_end__ - __bss_start__) / 4) 
	/** Add each additional bss section here */
	/* 
		LONG (__bss2_start__)
		LONG ((__bss2_end__ - __bss2_start__) / 4) 
	*/
		__nvm_zero_table_end__ = .;
	} > NVM_TEXT = 0

	/* Location counter can end up 2byte aligned with narrow Thumb code but
	   __etext is assumed by startup code to be the LMA of a section in RAM
	   which must be 4byte aligned */
	__etext = ALIGN (4);

	.data_romcode :
	{
		. = ALIGN(4);
		__data_romcode_start__ = .;
		. = . + (__romcode_ram_end_ - __RAM_BASE);
		. = ALIGN(4);
		/* All data end */
		__data_romcode_end__ = .;
	}

	/* This section holds the initialized variables and is listed in the copy table.
	 */
	.data : AT (__etext)
	{
		/*. = ALIGN(4);*/
		__data_start__ = .;
		*(vtable)
		*(.data)
		*(.data.*)
        
		. = ALIGN(4);
		/* preinit data */
		PROVIDE_HIDDEN (__preinit_array_start = .);
		KEEP(*(.preinit_array))
		PROVIDE_HIDDEN (__preinit_array_end = .);

		. = ALIGN(4);
		/* init data */
		PROVIDE_HIDDEN (__init_array_start = .);
		KEEP(*(SORT(.init_array.*)))
		KEEP(*(.init_array))
		PROVIDE_HIDDEN (__init_array_end = .);


		. = ALIGN(4);
		/* finit data */
		PROVIDE_HIDDEN (__fini_array_start = .);
		KEEP(*(SORT(.fini_array.*)))
		KEEP(*(.fini_array))
		PROVIDE_HIDDEN (__fini_array_end = .);

		KEEP(*(.jcr*))
		. = ALIGN(4);
		/* All data end */
		__data_end__ = .;

	} > RAM

	/** 
	  * Secondary data section, optional 
	  * 
	  * Remember to add each additional data section
	  * to the .copy.table above to asure proper
	  * initialization during startup.
	  */
	/*
	__etext2 = ALIGN (4);

	.data2 : AT (__etext2)
	{
		. = ALIGN(4);
		__data2_start__ = .;
		*(.data2)
		*(.data2.*)
		. = ALIGN(4);
		__data2_end__ = .;

	} > RAM 
	*/

	/* The SMACK DMA requires placing the channel descriptor block at a 1024 Byte
       aligned address with a size of 512 Bytes. We place it as last RAM memory
       section to minimize the amount of wasted memory. 
       Background is the following consideration:
       - Dandelion as a platform supports up to 10 (16 due to alignment restrictions) channels, 
         with each 16 bytes primary and 16 bytes alternate descriptors.
         This yields a descriptor size of 16x16x2 = 512 bytes.
         Since 8k of RAM are supported we need additional 3 address bits
         to specify the base address (CORE_SCU_DMA_CFG.DDBA).
       - Smack supports 10 DMA channels, only, 
         with each 16 bytes primary and 16 bytes alternate descriptors.
         This yields a descriptor size of 10x16x2 = 320 bytes.
         Still, the alignment of the DMA descriptor must use the  
         Dandelion platform alignment of 512 bytes.
         Note: this leaves 192 bytes unused, 96 above DMA descriptors, and 96 above alternate DMA descriptors!
       */
	/* Only the remaining RAM2 space is therefore available for NVM located firmware */
	.ram2_dma  (NOLOAD) :
	{
		. = ALIGN (4);
		__ram2_dma_section_start__ = .;
		. = . + (__romcode_ram2_end_ - __RAM2_BASE);
		__ram2_dma_section_end__ = .;
	} > RAM2

	.ram2  (NOLOAD) :
	{
		. = ALIGN (4);
		*(.ram2)
		*(.ram2.*);		
		__ram2_section_end__ = .;
	} > RAM2

	.bss :
	{
		. = ALIGN(4);
		__bss_start__ = .;
		*(.bss)
		*(.bss.*)
		*(COMMON)
		. = ALIGN(4);
		__bss_end__ = .;
	} > RAM

	/**
	 * Secondary bss section, optional 
	 *
	 * Remember to add each additional bss section
	 * to the .zero.table above to asure proper
	 * initialization during startup.
	 */
	/*
	.bss2 :
	{
		. = ALIGN(4);
		__bss2_start__ = .;
		*(.bss2)
		*(.bss2.*)
		*(COMMON)
		. = ALIGN(4);
		__bss2_end__ = .;
	} > RAM
	*/

	.noinit (NOLOAD) :
	{
		. = ALIGN(4);
		__noinit_start__ = .;
		*(.noinit)
		*(.noinit.*)
		. = ALIGN(4);
		__noinit_end__ = .;
	} > RAM

	.heap :
	{
		. = ALIGN(4);
		__end__ = .;
		PROVIDE(end = .);
		. = . + __HEAP_SIZE;
		. = ALIGN(4);
		__HeapLimit = .;
	} > RAM
    	
	.stack :
	{
		. = ORIGIN(RAM) + LENGTH(RAM) - __STACK_SIZE;
		. = ALIGN(4);
		__StackLimit = .;
		. = . + __STACK_SIZE;
		. = ALIGN(4);
		__StackTop = .;
	} > RAM
	PROVIDE(__stack = __StackTop);

	/* Check if data + heap + stack exceeds RAM limit */
	ASSERT(__StackLimit >= __HeapLimit, "region RAM overflowed with stack")

	/* ------------------------------------------------------------------------ */
	/* Code Space Padding
	 * The GNU linker seems to have problems with filling the unused code space area with
	 * padding Bytes. The following section starts behind the '.code_text' section
	 * and the attached '.data' load section, and it ends before
	 * the '.version' section. Writing a single pad Byte at the end of the
	 * section trigger the padding fill operation. */

	/* smackfw_dand_mockup:
	 *
	 * Here we list all NVM memory regions beyond the text segment, including all the paddings.
	 */

	/* padding of NVM_TEXT */
	/*pad_text_start = __etext;*/
	__data_size__ = __data_end__ - __data_start__;
	pad_text_start = __etext + __data_size__;	/* adjustment needed due to post build scripts and their requirements into this linker script */
	pad_text_end = __NVM_LOG_BASE;
	pad_text_size = pad_text_end - pad_text_start - 1;
	.text.pad_text pad_text_start :
	{
		. = . + pad_text_size;
		BYTE(0xff);
		/* we fill the rest of the code section with 0xffff:
		This resembles an erased NVM. */
	} > NVM_TEXT = 0xffff


	/* smackfw_dand_mockup:
	 * Log memory + padding
	 */
	.nvm.data_log __NVM_LOG_BASE /* (NOLOAD) */ :
	{
		__nvm_log_start__ = .;
		*(.nvm_log*);
		__nvm_log_end__ = .;
	} > NVM_DYNAMIC_HIGH = 0xffff

	/* Pad remainder of NVM sector with "empty" value */
	/* If section is consumed at this point, you have to comment the following padding */
	pad_log_start = __nvm_log_end__;
	pad_log_end = (__NVM_SECTORS_DYNAMIC_HIGH_BASE + __NVM_SECTORS_DYNAMIC_HIGH_SIZE);
	pad_log_size = pad_log_end - pad_log_start;
	.text.pad_log __nvm_log_end__ :
	{
		. = . + pad_log_size - 1;
		BYTE(0xff);
	} > NVM_DYNAMIC_HIGH = 0xffff


	/* smackfw_dand_mockup:
	 * User configuration memory + padding
	 */
	.nvm.data_user __NVM_USER_BASE /* (NOLOAD) */ :
	{
		__nvm_user_start__ = .;
		*(.nvm_user*);
		__nvm_user_end__ = .;
	} > NVM_DYNAMIC_LOW = 0xffff

	/* If section is consumed at this point, you have to comment the following padding */
	pad_user_start = __nvm_user_end__;
	pad_user_end = __NVM_CONFIG_BASE;
	pad_user_size = pad_user_end - pad_user_start;
	.text.pad_user __nvm_user_end__ :
	{
		. = . + pad_user_size - 1;
		BYTE(0xff);
		/* we fill the rest of the code section with 0xffff:
		This resembles an erased NVM. */
	} > NVM_DYNAMIC_LOW = 0xffff


	/* smackfw_dand_mockup:
	 * Persistent configuration memory + padding
	 */
	.nvm.data_config __NVM_CONFIG_BASE /* (NOLOAD) */ :
	{
		__nvm_config_start__ = .;
		*(.nvm_config*);
		__nvm_config_end__ = .;
	} > NVM_DYNAMIC_LOW = 0xffff

	/* smackfw_dand_mockup:
	 * My custom configuration memory + padding
	 */
	.nvm.data_my_config __NVM_MY_CONFIG_BASE /* (NOLOAD) */ :
	{
		__nvm_my_config_start__ = .;
		*(.nvm_my_config*);
		__nvm_my_config_end__ = .;
	} > NVM_DYNAMIC_LOW = 0xffff

	/* If section is consumed at this point, you have to comment the following padding */
	pad_my_config_start = __nvm_my_config_end__;
	pad_my_config_end = (__NVM_SECTORS_DYNAMIC_LOW_BASE + __NVM_SECTORS_DYNAMIC_LOW_SIZE);
	pad_my_config_size = pad_my_config_end - pad_my_config_start;
	.text.pad_my_config __nvm_my_config_end__ :
	{
		. = . + pad_my_config_size - 1;
		BYTE(0xff);
	} > NVM_DYNAMIC_LOW = 0xffff


	/* smackfw_dand_mockup:
	 * NDEF type 2 tag memory + padding
	 */
	.nvm.data_ndef __NVM_NDEF_BASE /* (NOLOAD) */ :
	{
		__nvm_ndef_start__ = .;
		*(.nvm_ndef*);
		__nvm_ndef_end__ = .;
	} > NVM_STATIC = 0xffff

	/* If section is consumed at this point, you have to comment the following padding */
	pad_ndef_start = __nvm_ndef_end__;
	pad_ndef_end = __NVM_CONSTANT_BASE;
	pad_ndef_size = pad_ndef_end - pad_ndef_start;
	.text.pad_ndef __nvm_ndef_end__ :
	{
		. = . + pad_ndef_size - 1;
		BYTE(0xff);
		/* we fill the rest of the code section with 0xffff:
		This resembles an erased NVM. */
	} > NVM_STATIC = 0xffff


	/* smackfw_dand_mockup:
	 * constant device data + padding
	 */
	.nvm.data_const __NVM_CONSTANT_BASE /* (NOLOAD) */ :
	{
		__nvm_const_start__ = .;
		KEEP(*(.nvm_const*));
		__nvm_const_end__ = .;
	} > NVM_STATIC = 0xffff

	/* If section is consumed at this point, you have to comment the following padding */
	pad_const_start = __nvm_const_end__;
	pad_const_end = (__NVM_SECTORS_STATIC_BASE + __NVM_SECTORS_STATIC_SIZE);
	pad_const_size = pad_const_end - pad_const_start;
	.text.pad_const __nvm_const_end__ :
	{
		. = . + pad_const_size - 1;
		BYTE(0xff);
		/* we fill the rest of the code section with 0xffff:
		This resembles an erased NVM. */
	} > NVM_STATIC = 0xffff

	/* smackfw_nvm_write:
	 *
	 * The following section provides padding of unused locations in the last NVM page before the version information.
	 * The section for the version information is defined somewhere above.
	 */
	/* reserve space for custom firmware version at end of padded region */
	__NVM_PADDING_SIZE = __NVM_PADDING_SIZE - 4;

	.text.pad_version __NVM_PADDING_BASE :
	{
		. = . + __NVM_PADDING_SIZE - 1;
		BYTE(0xff);
		/* we fill the rest of the code section with 0xffff:
		This resembles an erased NVM. */
	} > NVM = 0xffff

	/* custom firmware version: 4 bytes */
	__FIRMWARE_VERSION_BASE = __NVM_PADDING_BASE + __NVM_PADDING_SIZE;
	.text.fw_version __FIRMWARE_VERSION_BASE :
	{
		*(.fw_version*);
	} > NVM = 0xffff

	/* ------------------------------------------------------------------------ */

}
