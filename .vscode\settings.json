{"files.associations": {"*.js": "javascriptreact", "chrono": "c", "stdbool.h": "c", "buck.h": "c", "core_cm0.h": "c", "sys_tim_lib.h": "c", "sys_tick_lib.h": "c", "cstdlib": "c", "algorithm": "c", "time.h": "c", "handlers.h": "c", "optional": "c", "ratio": "c", "system_error": "c", "array": "c", "functional": "c", "regex": "c", "tuple": "c", "type_traits": "c", "utility": "c", "istream": "c", "ostream": "c", "variant": "c", "i2c_lib.h": "c", "settings.h": "c", "version.h": "c", "util.h": "c", "soft_i2c.h": "c", "string.h": "c", "soft_i2c_example.h": "c", "smack_dand_mockup.h": "c", "sensor_util.h": "c"}, "liveServer.settings.port": 5501}