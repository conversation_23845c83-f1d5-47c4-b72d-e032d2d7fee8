/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     my_exchange_handler.h
 *  @brief    Custom data exchange handler for NFC lock firmware
 *
 *  This module provides a centralized handler for various data exchange functions
 *  based on mailbox content[1] values.
 */

#ifndef _MY_EXCHANGE_HANDLER_H_
#define _MY_EXCHANGE_HANDLER_H_

// standard libs
#include "core_cm0.h"
#include <stdbool.h>

// Smack ROM lib
#include "rom_lib.h"

#ifdef __cplusplus
extern "C" {
#endif

// Function codes for mailbox content[1]
#define MY_EXCHANGE_FUNC_CONFIG_MODIFY      0x01    // Modify my_config values
#define MY_EXCHANGE_FUNC_USER_KEY_ERASE     0x02    // Erase user key (requires SU key verification)
#define MY_EXCHANGE_FUNC_RF_STATUS          0x03    // Get RF field status and RSSI/VCCCA
#define MY_EXCHANGE_FUNC_SENSOR_READ        0x04    // Read sensor values

/**
 * @brief Main exchange handler function
 *
 * This function processes the mailbox content[1] value to determine which
 * specific function to call.
 *
 * @return void
 */
void my_exchange_handler(void);

/**
 * @brief Handle config modification (content[1] = 0x01)
 *
 * Modifies my_config values based on mailbox content[2-9].
 */
void handle_config_modify(void);

/**
 * @brief Handle user key erase (content[1] = 0x02)
 *
 * Verifies the provided SU key against the stored SU key and erases
 * the user key if verification succeeds.
 */
void handle_user_key_erase(void);

/**
 * @brief Handle RF status read (content[1] = 0x03)
 *
 * Checks RF field presence and reads RSSI/VCCCA values.
 */
void handle_rf_status(void);

/**
 * @brief Handle sensor read (content[1] = 0x04)
 *
 * Reads sensor values (x, y, z, temperature) and target status.
 */
void handle_sensor_read(void);

#ifdef __cplusplus
}
#endif

#endif /* _MY_EXCHANGE_HANDLER_H_ */
